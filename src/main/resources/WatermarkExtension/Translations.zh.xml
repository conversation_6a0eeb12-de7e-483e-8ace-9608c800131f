<?xml version="1.1" encoding="UTF-8"?>
<xwikidoc version="1.5" reference="WatermarkExtension.Translations" locale="zh">
  <web>WatermarkExtension</web>
  <name>Translations</name>
  <language>zh</language>
  <defaultLanguage/>
  <translation>1</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>WatermarkExtension.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>水印扩展翻译</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>plain/1.0</syntaxId>
  <hidden>true</hidden>
  <object>
    <name>WatermarkExtension.Translations</name>
    <number>0</number>
    <className>XWiki.TranslationDocumentClass</className>
    <guid>watermark-translations-zh</guid>
    <class>
      <name>XWiki.TranslationDocumentClass</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <scope>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
        <customDisplay/>
        <disabled>0</disabled>
        <name>scope</name>
        <number>1</number>
        <prettyName>Scope</prettyName>
        <size>30</size>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </scope>
    </class>
    <property>
      <scope>WIKI</scope>
    </property>
  </object>
  <content>## 水印扩展翻译
## 中文翻译

# 管理界面
admin.watermark=水印
admin.watermark.description=配置Wiki的水印设置
admin.watermark.configuration=水印配置
admin.category.watermark.icon=icon-picture
watermark.admin.title=水印配置
watermark.admin.tooltip=配置Wiki的水印设置
watermark.admin.configuration=水印配置
watermark.admin.category=水印
watermark.admin.save=保存配置
watermark.admin.reset=重置为默认值
watermark.admin.preview=预览水印

# 配置字段
watermark.admin.enabled=启用水印
watermark.admin.enabled.help=启用或禁用整个Wiki的水印显示

watermark.admin.textTemplate=水印文本模板
watermark.admin.textTemplate.help=水印的文本模板。使用 ${user} 表示当前用户，${timestamp} 表示当前时间

watermark.admin.xSpacing=水平间距
watermark.admin.xSpacing.help=水印之间的水平间距，单位为像素 (50-500)

watermark.admin.ySpacing=垂直间距
watermark.admin.ySpacing.help=水印之间的垂直间距，单位为像素 (50-500)

watermark.admin.angle=旋转角度
watermark.admin.angle.help=水印文本的旋转角度，单位为度 (-180 到 180)

watermark.admin.opacity=透明度
watermark.admin.opacity.help=水印的透明度级别 (0.0 = 完全透明，1.0 = 完全不透明)

watermark.admin.fontSize=字体大小
watermark.admin.fontSize.help=水印文本的字体大小，单位为像素 (8-48)

watermark.admin.antiCopy=防复制保护
watermark.admin.antiCopy.help=启用防复制保护功能，防止内容复制和右键操作

watermark.admin.applyToMobile=应用到移动设备
watermark.admin.applyToMobile.help=在移动设备和平板电脑上启用水印显示

# 消息
watermark.admin.save.success=水印配置保存成功
watermark.admin.reset.success=水印配置已重置为默认值
watermark.admin.reset.confirm=您确定要将所有水印设置重置为默认值吗？

# 预览
watermark.admin.preview.placeholder=水印预览将在此处显示
watermark.admin.preview.help=此预览显示水印在页面上的显示效果
watermark.admin.preview.disabled=水印已禁用

# 配置类属性
WatermarkExtension.WatermarkConfigClass_enabled=启用水印
WatermarkExtension.WatermarkConfigClass_textTemplate=文本模板
WatermarkExtension.WatermarkConfigClass_xSpacing=水平间距
WatermarkExtension.WatermarkConfigClass_ySpacing=垂直间距
WatermarkExtension.WatermarkConfigClass_angle=旋转角度
WatermarkExtension.WatermarkConfigClass_opacity=透明度
WatermarkExtension.WatermarkConfigClass_fontSize=字体大小
WatermarkExtension.WatermarkConfigClass_antiCopy=防复制保护
WatermarkExtension.WatermarkConfigClass_applyToMobile=应用到移动设备

# 错误消息
watermark.error.config.load=加载水印配置失败
watermark.error.config.save=保存水印配置失败
watermark.error.canvas.unsupported=此浏览器不支持Canvas
watermark.error.permission.denied=没有权限修改水印设置

# 状态消息
watermark.status.enabled=水印已启用
watermark.status.disabled=水印已禁用
watermark.status.loading=正在加载水印配置...
watermark.status.saving=正在保存水印配置...

# 帮助和文档
watermark.help.placeholders=可用占位符：${user} (当前用户)，${timestamp} (当前时间)
watermark.help.performance=较大的间距值和较低的透明度可以提高性能
watermark.help.mobile=移动端优化会自动调整水印参数
watermark.help.anticopy=防复制保护可能会影响正常的用户交互

# 验证消息
watermark.validation.xSpacing=值必须在 50 到 500 之间
watermark.validation.ySpacing=值必须在 50 到 500 之间
watermark.validation.angle=值必须在 -180 到 180 之间
watermark.validation.opacity=值必须在 0.0 到 1.0 之间
watermark.validation.fontSize=值必须在 8 到 48 之间</content>
</xwikidoc>
