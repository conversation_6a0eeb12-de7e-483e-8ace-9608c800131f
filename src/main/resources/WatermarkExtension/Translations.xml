<?xml version="1.1" encoding="UTF-8"?>
<xwikidoc version="1.5" reference="WatermarkExtension.Translations" locale="">
  <web>WatermarkExtension</web>
  <name>Translations</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>WatermarkExtension.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>Watermark Extension Translations</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>plain/1.0</syntaxId>
  <hidden>true</hidden>
  <object>
    <name>WatermarkExtension.Translations</name>
    <number>0</number>
    <className>XWiki.TranslationDocumentClass</className>
    <guid>watermark-translations-en</guid>
    <class>
      <name>XWiki.TranslationDocumentClass</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <scope>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
        <customDisplay/>
        <disabled>0</disabled>
        <name>scope</name>
        <number>1</number>
        <prettyName>Scope</prettyName>
        <size>30</size>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </scope>
    </class>
    <property>
      <scope>WIKI</scope>
    </property>
  </object>
  <content>## Watermark Extension Translations
## English (Default) Translations

# Admin Interface
admin.watermark=Watermark
admin.watermark.description=Configure watermark settings for the wiki
admin.watermark.configuration=Watermark Configuration
admin.category.watermark.icon=icon-picture
watermark.admin.title=Watermark Configuration
watermark.admin.tooltip=Configure watermark settings for the wiki
watermark.admin.configuration=Watermark Configuration
watermark.admin.category=Watermark
watermark.admin.save=Save Configuration
watermark.admin.reset=Reset to Defaults
watermark.admin.preview=Preview Watermark

# Configuration Fields
watermark.admin.enabled=Enable Watermark
watermark.admin.enabled.help=Enable or disable the watermark display across the wiki

watermark.admin.textTemplate=Watermark Text Template
watermark.admin.textTemplate.help=Text template for the watermark. Use ${user} for current user and ${timestamp} for current time

watermark.admin.xSpacing=Horizontal Spacing
watermark.admin.xSpacing.help=Horizontal spacing between watermarks in pixels (50-500)

watermark.admin.ySpacing=Vertical Spacing
watermark.admin.ySpacing.help=Vertical spacing between watermarks in pixels (50-500)

watermark.admin.angle=Rotation Angle
watermark.admin.angle.help=Rotation angle of the watermark text in degrees (-180 to 180)

watermark.admin.opacity=Opacity
watermark.admin.opacity.help=Transparency level of the watermark (0.0 = transparent, 1.0 = opaque)

watermark.admin.fontSize=Font Size
watermark.admin.fontSize.help=Font size of the watermark text in pixels (8-48)

watermark.admin.antiCopy=Anti-Copy Protection
watermark.admin.antiCopy.help=Enable anti-copy protection to prevent content copying and right-click

watermark.admin.applyToMobile=Apply to Mobile Devices
watermark.admin.applyToMobile.help=Enable watermark display on mobile devices and tablets

# Messages
watermark.admin.save.success=Watermark configuration saved successfully
watermark.admin.reset.success=Watermark configuration reset to default values
watermark.admin.reset.confirm=Are you sure you want to reset all watermark settings to default values?

# Preview
watermark.admin.preview.placeholder=Watermark preview will appear here
watermark.admin.preview.help=This preview shows how the watermark will appear on pages
watermark.admin.preview.disabled=Watermark is disabled

# Configuration Class Properties
WatermarkExtension.WatermarkConfigClass_enabled=Enable Watermark
WatermarkExtension.WatermarkConfigClass_textTemplate=Text Template
WatermarkExtension.WatermarkConfigClass_xSpacing=Horizontal Spacing
WatermarkExtension.WatermarkConfigClass_ySpacing=Vertical Spacing
WatermarkExtension.WatermarkConfigClass_angle=Rotation Angle
WatermarkExtension.WatermarkConfigClass_opacity=Opacity
WatermarkExtension.WatermarkConfigClass_fontSize=Font Size
WatermarkExtension.WatermarkConfigClass_antiCopy=Anti-Copy Protection
WatermarkExtension.WatermarkConfigClass_applyToMobile=Apply to Mobile

# Error Messages
watermark.error.config.load=Failed to load watermark configuration
watermark.error.config.save=Failed to save watermark configuration
watermark.error.canvas.unsupported=Canvas is not supported in this browser
watermark.error.permission.denied=Permission denied to modify watermark settings

# Status Messages
watermark.status.enabled=Watermark is enabled
watermark.status.disabled=Watermark is disabled
watermark.status.loading=Loading watermark configuration...
watermark.status.saving=Saving watermark configuration...

# Help and Documentation
watermark.help.placeholders=Available placeholders: ${user} (current user), ${timestamp} (current time)
watermark.help.performance=Large spacing values and low opacity improve performance
watermark.help.mobile=Mobile optimization automatically adjusts watermark parameters
watermark.help.anticopy=Anti-copy protection may affect normal user interactions

# Validation Messages
watermark.validation.xSpacing=Value must be between 50 and 500
watermark.validation.ySpacing=Value must be between 50 and 500
watermark.validation.angle=Value must be between -180 and 180
watermark.validation.opacity=Value must be between 0.0 and 1.0
watermark.validation.fontSize=Value must be between 8 and 48</content>
</xwikidoc>
